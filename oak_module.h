/*
 *        LICENSE:
 *        (C)Copyright 2021-2022 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: functions as called from Linux kernel (entry points)
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak
 * Author: <PERSON><PERSON>
 * Date  : 2021-11-15  - 23:29
 */

int	oak_init_module(void);
void	oak_exit_module(void);

module_init(oak_init_module);
module_exit(oak_exit_module);

module_param(debug, int, 0);
MODULE_PARM_DESC(debug, "OAK debug level");

/* RX and TX ring sizes are given as a power of 2 e.g:
 * x=[0-7] :: ring-size=2^(4+x), where x is the specified load parameter.
 */
module_param(rxs, int, 0);
MODULE_PARM_DESC(rxs, "Receive ring size");

module_param(txs, int, 0);
MODULE_PARM_DESC(txs, "Transmit ring size");

module_param(chan, int, 0);
MODULE_PARM_DESC(chan, "Number of (tx/rx) channels");

module_param(rto, int, 0);
MODULE_PARM_DESC(rto, "Receive descriptor timeout in usec");

module_param(mhdr, int, 0);
MODULE_PARM_DESC(mhdr, "Marvell header generation");

module_param(port_speed, int, 0);
MODULE_PARM_DESC(mhdr, "Unimac 11 Port speed");

module_param(napi_wt, int, 0);
MODULE_PARM_DESC(napi_wt, "NAPI Poll weight/budget");

module_param(jumbo, int, 0);
MODULE_PARM_DESC(jumbo, "Enable/Disable jumbo support, enabled by default");
MODULE_LICENSE("GPL");
