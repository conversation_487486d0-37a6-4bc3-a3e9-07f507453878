/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: functions as called from Linux kernel (entry points)
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak
 * Author: afischer
 * Date  :23.02.00035-06  - 11:03
 */
#ifndef H_OAK
#define H_OAK

/* Include for relation to classifier oak_unimac */
#include "oak_unimac.h"
/* Include for relation to classifier oak_net */
#include "oak_net.h"
/* Include for relation to classifier oak_ethtool */
#include "oak_ethtool.h"
#include "oak_debug.h"
/* Include for relation to classifier oak_module */
#include "oak_module.h"
#include "oak_chksum.h"
#include "oak_dpm.h"
#include "oak_ptp.h"

#define OAK_DRIVER_NAME "oak"

#define OAK_DRIVER_STRING "Marvell PCIe Switch Driver"

#define OAK_DRIVER_VERSION "3.02.0003"

#define OAK_DRIVER_COPYRIGHT "Copyright (c) Marvell - 2018"

#define OAK_MAX_JUMBO_FRAME_SIZE (10 * 1024)
#define OAK_FRAME_SIZE_1500 1500

/* EPU data register offset definition */
#define OAK_EPU_DATA2 0xF208
#define OAK_EPU_DATA3 0xF20C

u32 debug;
u32 txs;
u32 rxs;
int chan;
int rto;
int mhdr;
u32 port_speed;
int jumbo;

#endif /* #ifndef H_OAK */

