/* ***************************************************
 * Model File: OAK-Linux::OAK Switch Board - Linux Driver::Class
 * Model::oak_unimac_desc
 * Model Path: oak_linux_k5 --- DBType=0;Connect=Provider=MSDASQL.1;Persist
 * Security Info=False;Data Source=oak_linux_k5;LazyLoad=1;
 *
 * 2019-12-16  - 11:02
 * ***************************************************
 */
#ifndef H_OAK_UNIMAC_DESC
#define H_OAK_UNIMAC_DESC

typedef struct oak_rxd_tstruct {
	u32 buf_ptr_lo;
	u32 buf_ptr_hi;
} oak_rxd_t;

typedef struct oak_rxs_tstruct {
	u32 bc : 16;
	u32 es : 1;
	u32 ec : 2;
	u32 res1 : 1;
	u32 first_last : 2;
	u32 ipv4_hdr_ok : 1;
	u32 l4_chk_ok : 1;
	u32 l4_prot : 2;
	u32 res2 : 1;
	u32 l3_ipv4 : 1;
	u32 l3_ipv6 : 1;
	u32 vlan : 1;
	u32 l2_prot : 2;
	u32 timestamp : 32;
	u32 rc_chksum : 16;
	u32 udp_cs_0 : 1;
	u32 res3 : 15;
	u32 mhdr : 16;
	u32 mhok : 1;
	u32 res4 : 15;
} oak_rxs_t;

typedef struct oak_txd_tstruct {
	u32 bc : 16;
	u32 res1 : 4;
	u32 last : 1;
	u32 first : 1;
	u32 gl3_chksum : 1;
	u32 gl4_chksum : 1;
	u32 res2 : 4;
	u32 time_valid : 1;
	u32 res3 : 3;
	u32 timestamp : 32;
	u32 buf_ptr_lo : 32;
	u32 buf_ptr_hi : 32;
} oak_txd_t;

#endif /* #ifndef H_OAK_UNIMAC_DESC */

