/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: collection of functions that provide user IO-control requests
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak_ioc_lgen
 * Author: afischer
 * Date  :2019-12-16  - 11:02
 */
#ifndef H_OAK_IOC_LGEN
#define H_OAK_IOC_LGEN

typedef struct oak_ioc_lgenstruct {
#define OAK_IOCTL_LGEN (SIOCDEVPRIVATE + 3)
#define OAK_IOCTL_LGEN_INIT	BIT(0)
#define OAK_IOCTL_LGEN_TX_DATA	BIT(1)
#define OAK_IOCTL_LGEN_TX_START BIT(2)
#define OAK_IOCTL_LGEN_TX_STOP	BIT(3)
#define OAK_IOCTL_LGEN_RX_START BIT(4)
#define OAK_IOCTL_LGEN_RX_STOP	BIT(5)
#define OAK_IOCTL_LGEN_RX_DATA	BIT(6)
#define OAK_IOCTL_LGEN_RELEASE	BIT(7)
#define OAK_IOCTL_LGEN_TX_RESET BIT(8)
#define OAK_IOCTL_LGEN_RX_RESET BIT(9)
	__u32 cmd;
	__u32 offs;
	__u32 len;
	__u32 channel;
	__u32 count;
	char data[32];
	int error;
} oak_ioc_lgen;

#endif /* #ifndef H_OAK_IOC_LGEN */
