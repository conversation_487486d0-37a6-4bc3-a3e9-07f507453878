/* ***************************************************
 * Model File: OAK-Linux::OAK Switch Board - Linux Driver::Class Model::ldg_t
 * Model Path: oak_linux_k5 --- DBType=0;Connect=Provider=MSDASQL.1;Persist
 * Security Info=False;Data Source=oak_linux_k5;LazyLoad=1;
 *
 * 2019-12-16  - 11:02
 * ***************************************************
 */
#ifndef H_LDG_T
#define H_LDG_T

typedef struct ldg_tstruct {
	struct napi_struct napi;
	struct oak_tstruct *device;
	u64 msi_tx;
	u64 msi_rx;
	u64 msi_te;
	u64 msi_re;
	u64 msi_ge;
	u64 irq_mask;
	u32 irq_first;
	u32 irq_count;
	u32 msi_grp;
	char msiname[32];
} ldg_t;

#endif /* #ifndef H_LDG_T */

