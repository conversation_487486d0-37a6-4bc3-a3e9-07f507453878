/* ***************************************************
 * Model File: OAK-Linux::OAK Switch Board - Linux Driver::Class
 * Model::oak_ioc_flow
 * Model Path: oak_linux_k5 --- DBType=0;Connect=Provider=MSDASQL.1;Persist
 * Security Info=False;Data Source=oak_linux_k5;LazyLoad=1;
 *
 * 2019-12-16  - 11:02
 * ***************************************************
 */
#ifndef H_OAK_IOC_FLOW
#define H_OAK_IOC_FLOW

typedef struct oak_ioc_flowstruct {
#define OAK_IOCTL_RXFLOW (SIOCDEVPRIVATE + 8)
#define OAK_IOCTL_RXFLOW_CLEAR	0
#define OAK_IOCTL_RXFLOW_MGMT	BIT(0)
#define OAK_IOCTL_RXFLOW_QPRI	BIT(3)
#define OAK_IOCTL_RXFLOW_SPID	BIT(7)
#define OAK_IOCTL_RXFLOW_FLOW	BIT(12)
#define OAK_IOCTL_RXFLOW_DA	BIT(18)
#define OAK_IOCTL_RXFLOW_ET	BIT(19)
#define OAK_IOCTL_RXFLOW_FID	BIT(20)
#define OAK_IOCTL_RXFLOW_DA_MASK BIT(21)
	__u32 cmd;
	__u32 idx;
	u32 val_lo;
	u32 val_hi;
	char data[16];
	int ena;
	int error;
} oak_ioc_flow;

#endif /* #ifndef H_OAK_IOC_FLOW */

