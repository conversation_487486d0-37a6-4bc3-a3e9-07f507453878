# Oak驱动PTP时钟框架设计

## 概述

本文档描述了为Oak网络驱动设计的PTP (Precision Time Protocol) 时钟框架。该框架支持IEEE 1588精确时间协议，为网络时间同步提供硬件级支持。

## 硬件时间戳格式

硬件提供以下时间戳格式：
- **32位纳秒** (0-999,999,999)
- **48位秒** (足够使用很长时间)

这种格式允许我们直接使用硬件时间戳，无需复杂的计数器转换。

## 文件结构

### 新增文件

1. **oak_ptp.h** - PTP功能头文件
   - PTP寄存器定义
   - 函数声明
   - 配置常量

2. **oak_ptp.c** - PTP功能实现
   - PTP时钟操作
   - 时间戳处理
   - 配置管理

### 修改文件

1. **oak_unimac.h** - 添加PTP相关结构体成员
2. **oak.c** - 集成PTP初始化和清理
3. **oak_net.c** - 添加时间戳处理和ioctl支持
4. **Makefile** - 添加PTP模块编译

## 核心数据结构

### oak_t结构体新增成员

```c
#ifdef CONFIG_PTP_1588_CLOCK
    struct ptp_clock *ptp_clock;           // PTP时钟设备
    struct ptp_clock_info ptp_caps;        // PTP时钟能力
    spinlock_t ptp_lock;                   // PTP操作锁
    u32 ptp_enabled;                       // PTP使能标志
    struct hwtstamp_config tstamp_config;  // 时间戳配置
#endif
```

## 主要功能模块

### 1. PTP时钟注册

- `oak_ptp_init()` - 初始化并注册PTP时钟
- `oak_ptp_remove()` - 注销PTP时钟
- `oak_ptp_start()` - 启动PTP时钟
- `oak_ptp_stop()` - 停止PTP时钟

### 2. 时间操作

- `oak_ptp_gettime64()` - 获取当前时间
- `oak_ptp_settime64()` - 设置时间
- `oak_ptp_adjtime()` - 调整时间
- `oak_ptp_adjfreq()` - 调整频率

### 3. 时间戳处理

- `oak_ptp_tx_timestamp()` - 处理发送时间戳
- `oak_ptp_rx_timestamp()` - 处理接收时间戳
- `oak_ptp_is_ptp_packet()` - 检测PTP包

### 4. 配置管理

- `oak_ptp_set_ts_config()` - 设置时间戳配置
- `oak_ptp_get_ts_config()` - 获取时间戳配置

## 硬件寄存器映射

### ToD (Time of Day) 寄存器

```c
#define OAK_PTP_TOD_NANO_SEC_LO     0x8008  /* 纳秒 [15:0] */
#define OAK_PTP_TOD_NANO_SEC_HI     0x800C  /* 纳秒 [31:16] */
#define OAK_PTP_TOD_SEC_LO          0x8010  /* 秒 [15:0] */
#define OAK_PTP_TOD_SEC_MID         0x8014  /* 秒 [31:16] */
#define OAK_PTP_TOD_SEC_HI          0x8018  /* 秒 [47:32] */
```

### 控制寄存器

```c
#define OAK_PTP_CLOCK_CTRL          0x8000  /* 控制寄存器 */
#define OAK_PTP_CLOCK_STATUS        0x8004  /* 状态寄存器 */
#define OAK_PTP_TIME_ADJ_SEC_LO     0x801C  /* 时间调整秒 */
#define OAK_PTP_TIME_ADJ_NANO       0x8024  /* 时间调整纳秒 */
#define OAK_PTP_FREQ_ADJ            0x8028  /* 频率调整 */
```

## 集成点

### 1. 驱动初始化 (oak.c)

```c
// 在oak_probe()中
err = oak_ptp_init(adapter);

// 在oak_remove()中  
oak_ptp_remove(adapter);
```

### 2. 网络接口控制 (oak_net.c)

```c
// 在oak_net_ioctl()中
if (cmd == SIOCSHWTSTAMP)
    retval = oak_ptp_set_ts_config(np, ifr);
if (cmd == SIOCGHWTSTAMP)
    retval = oak_ptp_get_ts_config(np, ifr);
```

### 3. 数据路径集成

#### 接收路径
```c
// 在oak_net_process_rx_pkt()中
oak_ptp_rx_timestamp(np, skb, rsr->timestamp);
```

#### 发送路径
```c
// 在oak_net_set_txd_first()中设置时间戳使能
if (oak_ptp_is_ptp_packet(skb)) {
    txd->time_valid = 1;
}

// 在oak_net_process_tx_pkt()中处理完成时间戳
if (txd->time_valid) {
    oak_ptp_tx_timestamp(np, skb, txd->timestamp);
}
```

## 支持的PTP功能

### 时间戳模式
- HWTSTAMP_TX_OFF/ON - 发送时间戳控制
- HWTSTAMP_FILTER_PTP_V2_EVENT - PTP v2事件消息过滤
- HWTSTAMP_FILTER_ALL - 所有包时间戳

### PTP包检测
- PTP over Ethernet (ETH_P_1588)
- PTP over UDP (端口319/320)
- IPv4和IPv6支持

## TODO项目

以下功能需要根据具体硬件实现：

1. **硬件寄存器访问**
   - 实现ToD寄存器读写
   - 实现控制寄存器操作
   - 实现频率调整

2. **时间戳转换**
   - 硬件时间戳到纳秒转换
   - 时钟频率校准

3. **高级功能**
   - 外部时间戳输入
   - 周期性输出
   - PPS (Pulse Per Second) 输出

## 使用方法

### 编译选项
确保内核配置中启用：
```
CONFIG_PTP_1588_CLOCK=y
```

### 用户空间工具
可以使用标准PTP工具：
- `ptp4l` - PTP守护进程
- `phc2sys` - 系统时钟同步
- `testptp` - PTP时钟测试工具

### 示例命令
```bash
# 启用PTP时间戳
ethtool -T eth0

# 设置时间戳模式
hwstamp_ctl -i eth0 -t 1 -r 1

# 运行PTP客户端
ptp4l -i eth0 -m
```

## 调试支持

框架包含详细的调试输出，可通过debug参数控制：
```bash
# 加载驱动时启用PTP调试
insmod oak_pci.ko debug=0x1000
```

## 性能考虑

1. **时间戳处理开销最小化**
   - 只对PTP包进行时间戳处理
   - 使用硬件时间戳避免软件转换

2. **锁竞争最小化**
   - PTP操作使用独立的锁
   - 时间戳处理在数据路径中快速完成

3. **内存使用优化**
   - 时间戳配置按需分配
   - 避免不必要的数据拷贝
