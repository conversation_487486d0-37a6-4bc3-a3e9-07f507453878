/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: collection of functions that provide user IO-control requests
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class
 * Model::oak_unimac_stat
 * Author: afischer
 * Date  :2019-12-16  - 11:02
 */
#ifndef H_OAK_UNIMAC_STAT
#define H_OAK_UNIMAC_STAT

typedef struct oak_unimac_statstruct {
	u64 rx_good_frames;
	u64 rx_bad_frames;
	u64 rx_stall_fifo;
	u64 rx_stall_desc;
	u64 rx_discard_desc;
	u64 tx_pause;
	u64 tx_stall_fifo;
} oak_unimac_stat;

#endif /* #ifndef H_OAK_UNIMAC_STAT */

