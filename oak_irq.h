/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak_irq
 * Author: afischer
 * Date  :2019-12-16  - 11:05
 */
#ifndef H_OAK_IRQ
#define H_OAK_IRQ

/* Include for relation to classifier oak_unimac */
#include "oak_unimac.h"

extern u32 debug;
struct oak_tstruct;
/* Name         : oak_irq_request_ivec
 * Returns      : int
 * Parameters   : struct oak_tstruct * np
 *  Description : This function request irq vector
 */
int oak_irq_request_ivec(struct oak_tstruct *np);

/* Name        : oak_irq_release_ivec
 * Returns     : void
 * Parameters  : struct oak_tstruct *np
 * Description : This function reset the MSI interrupt vector structure.
 */
void oak_irq_release_ivec(struct oak_tstruct *np);

/* Name        : oak_irq_enable_gicu_64
 * Returns     : void
 * Parameters  : struct oak_tstruct *np, u64 mask
 * Description : This function set the 64bit GICU mask.
 */
void oak_irq_enable_gicu_64(struct oak_tstruct *np, u64 mask);

/* Name        : oak_irq_ena_general
 * Returns     : void
 * Parameters  : struct oak_tstruct *np, u32 enable
 * Description : This function set the mask which serve general errors
 */
void oak_irq_ena_general(struct oak_tstruct *np, u32 enable);

/* Name      : enable_groups
 * Returns   : int
 * Parameters:  struct oak_tstruct *np
 * Description : This function enable the group irq
 */
int oak_irq_enable_groups(struct oak_tstruct *np);

/* Name        : oak_irq_disable_groups
 * Returns     : void
 * Parameters  : struct oak_tstruct *np
 * Description : This function disbles the group irq.
 */
void oak_irq_disable_groups(struct oak_tstruct *np);

#endif /* #ifndef H_OAK_IRQ */

