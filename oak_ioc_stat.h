/* ***************************************************
 * Model File: OAK-Linux::OAK Switch Board - Linux Driver::Class
 * Model::oak_ioc_stat
 * Model Path: oak_linux_k5 --- DBType=0;Connect=Provider=MSDASQL.1;Persist
 * Security Info=False;Data Source=oak_linux_k5;LazyLoad=1;
 *
 * 2019-12-16  - 11:02
 * ***************************************************
 */
#ifndef H_OAK_IOC_STAT
#define H_OAK_IOC_STAT

typedef struct oak_ioc_statstruct {
#define OAK_IOCTL_STAT (SIOCDEVPRIVATE + 4)
#define OAK_IOCTL_STAT_GET_TXS BIT(0)
#define OAK_IOCTL_STAT_GET_RXS BIT(1)
#define OAK_IOCTL_STAT_GET_TXC BIT(2)
#define OAK_IOCTL_STAT_GET_RXC BIT(3)
#define OAK_IOCTL_STAT_GET_RXB BIT(4)
#define OAK_IOCTL_STAT_GET_LDG BIT(5)
	__u32 cmd;
	__u32 idx;
	__u32 offs;
	char data[128];
	int error;
} oak_ioc_stat;

#endif /* #ifndef H_OAK_IOC_STAT */
