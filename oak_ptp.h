/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: PTP (Precision Time Protocol) support functions
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak_ptp
 * Author: [Your Name]
 * Date  : [Current Date]
 */

#ifndef H_OAK_PTP
#define H_OAK_PTP

#ifdef CONFIG_PTP_1588_CLOCK

#include <linux/ptp_clock_kernel.h>
#include <linux/clocksource.h>
#include <linux/timecounter.h>
#include <linux/net_tstamp.h>
#include "oak_unimac.h"


/* ToD (Time of Day) Registers - Hardware provides 48-bit seconds + 32-bit nanoseconds */
#define OAK_PTP_TOD_NANO_SEC_LO     0x8008  /* ToD Nano Seconds [15:0] */
#define OAK_PTP_TOD_NANO_SEC_HI     0x800C  /* ToD Nano Seconds [31:16] */
#define OAK_PTP_TOD_SEC_LO          0x8010  /* ToD Seconds [15:0] */
#define OAK_PTP_TOD_SEC_MID         0x8014  /* ToD Seconds [31:16] */
#define OAK_PTP_TOD_SEC_HI          0x8018  /* ToD Seconds [47:32] */

/* PTP Clock Parameters */
#define OAK_PTP_CLOCK_FREQ_HZ       250000000ULL  /* 250MHz */
#define OAK_PTP_MAX_ADJ             500000000     /* 500 ppm */
#define OAK_PTP_CYCLECOUNTER_MASK   0xFFFFFFFFULL

/* PTP Timestamp Modes */
#define OAK_PTP_TX_TIMESTAMP_ENABLE BIT(0)
#define OAK_PTP_RX_TIMESTAMP_ENABLE BIT(1)

/* Function Declarations */

/* Name        : oak_ptp_init
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Initialize PTP clock and register with kernel
 */
int oak_ptp_init(oak_t *np);

/* Name        : oak_ptp_remove
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Remove PTP clock and cleanup resources
 */
void oak_ptp_remove(oak_t *np);

/* Name        : oak_ptp_start
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Start PTP clock operation
 */
int oak_ptp_start(oak_t *np);

/* Name        : oak_ptp_stop
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Stop PTP clock operation
 */
void oak_ptp_stop(oak_t *np);

/* Name        : oak_ptp_adjfreq
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, s32 ppb
 * Description : Adjust PTP clock frequency
 */
int oak_ptp_adjfreq(struct ptp_clock_info *ptp, s32 ppb);

/* Name        : oak_ptp_adjtime
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, s64 delta
 * Description : Adjust PTP clock time by delta nanoseconds
 */
int oak_ptp_adjtime(struct ptp_clock_info *ptp, s64 delta);

/* Name        : oak_ptp_gettime64
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, struct timespec64 *ts
 * Description : Get current PTP clock time
 */
int oak_ptp_gettime64(struct ptp_clock_info *ptp, struct timespec64 *ts);

/* Name        : oak_ptp_settime64
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, const struct timespec64 *ts
 * Description : Set PTP clock time
 */
int oak_ptp_settime64(struct ptp_clock_info *ptp, const struct timespec64 *ts);

/* Name        : oak_ptp_enable
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, struct ptp_clock_request *rq, int on
 * Description : Enable/disable PTP features
 */
int oak_ptp_enable(struct ptp_clock_info *ptp, struct ptp_clock_request *rq, int on);

/* Name        : oak_ptp_read_clock
 * Returns     : u64
 * Parameters  : const struct cyclecounter *cc
 * Description : Read hardware clock counter
 */
u64 oak_ptp_read_clock(const struct cyclecounter *cc);

/* Name        : oak_ptp_tx_timestamp
 * Returns     : void
 * Parameters  : oak_t *np, struct sk_buff *skb, u32 timestamp
 * Description : Handle TX timestamp for PTP packets
 */
void oak_ptp_tx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp);

/* Name        : oak_ptp_rx_timestamp
 * Returns     : void
 * Parameters  : oak_t *np, struct sk_buff *skb, u32 timestamp
 * Description : Handle RX timestamp for PTP packets
 */
void oak_ptp_rx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp);

/* Name        : oak_ptp_is_ptp_packet
 * Returns     : bool
 * Parameters  : struct sk_buff *skb
 * Description : Check if packet is a PTP packet
 */
bool oak_ptp_is_ptp_packet(struct sk_buff *skb);

/* Name        : oak_ptp_get_ts_config
 * Returns     : int
 * Parameters  : oak_t *np, struct ifreq *ifr
 * Description : Get timestamp configuration
 */
int oak_ptp_get_ts_config(oak_t *np, struct ifreq *ifr);

/* Name        : oak_ptp_set_ts_config
 * Returns     : int
 * Parameters  : oak_t *np, struct ifreq *ifr
 * Description : Set timestamp configuration
 */
int oak_ptp_set_ts_config(oak_t *np, struct ifreq *ifr);

/* Name        : oak_ptp_reset_clock
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Reset PTP hardware clock
 */
int oak_ptp_reset_clock(oak_t *np);

/* Name        : oak_ptp_update_timecounter
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Update software timecounter
 */
void oak_ptp_update_timecounter(oak_t *np);

#else /* CONFIG_PTP_1588_CLOCK */

/* Stub functions when PTP is not enabled */
static inline int oak_ptp_init(oak_t *np) { return 0; }
static inline void oak_ptp_remove(oak_t *np) { }
static inline int oak_ptp_start(oak_t *np) { return 0; }
static inline void oak_ptp_stop(oak_t *np) { }
static inline void oak_ptp_tx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp) { }
static inline void oak_ptp_rx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp) { }
static inline bool oak_ptp_is_ptp_packet(struct sk_buff *skb) { return false; }
static inline int oak_ptp_get_ts_config(oak_t *np, struct ifreq *ifr) { return -EOPNOTSUPP; }
static inline int oak_ptp_set_ts_config(oak_t *np, struct ifreq *ifr) { return -EOPNOTSUPP; }

#endif /* CONFIG_PTP_1588_CLOCK */

#endif /* H_OAK_PTP */
