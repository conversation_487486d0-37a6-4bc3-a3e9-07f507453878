/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: collection of functions that provide user IO-control requests
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class
 * Model::oak_channel_stat
 * Author: afischer
 * Date  :2019-12-16  - 11:02
 */
#ifndef H_OAK_CHANNEL_STAT
#define H_OAK_CHANNEL_STAT

typedef struct oak_chan_infostruct {
	u32 flags;
	u32 r_count;
	u32 r_size;
	u32 r_pend;
	u32 r_widx;
	u32 r_ridx;
	u32 r_len;
} oak_chan_info;

typedef struct oak_driver_rx_statstruct {
	u64 channel;
	u64 rx_alloc_skbs;
	u64 rx_free_skbs;
	u64 rx_alloc_error;
	u64 rx_frame_error;
	u64 rx_errors;
	u64 rx_interrupts;
	u64 rx_goodframe;
	u64 rx_byte_count;
	u64 rx_vlan;
	u64 rx_badframe;
	u64 rx_no_sof;
	u64 rx_no_eof;
	u64 rx_badcrc;
	u64 rx_badcsum;
	u64 rx_l4p_ok;
	u64 rx_ip4_ok;
	u64 rx_nores;
	u64 rx_64;
	u64 rx_128;
	u64 rx_256;
	u64 rx_512;
	u64 rx_1024;
	u64 rx_2048;
	u64 rx_fragments;
} oak_driver_rx_stat;

typedef struct oak_driver_tx_statstruct {
	u64 channel;
	u64 tx_frame_count;
	u64 tx_frame_compl;
	u64 tx_byte_count;
	u64 tx_fragm_count;
	u64 tx_drop;
	u64 tx_errors;
	u64 tx_interrupts;
	u64 tx_stall_count;
	u64 tx_64;
	u64 tx_128;
	u64 tx_256;
	u64 tx_512;
	u64 tx_1024;
	u64 tx_2048;
} oak_driver_tx_stat;

#endif /* #ifndef H_OAK_CHANNEL_STAT */

