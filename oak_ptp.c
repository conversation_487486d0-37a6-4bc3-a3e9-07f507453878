/*
 *        LICENSE:
 *        (C)Copyright 2010-2011 Marvell.
 *
 *        All Rights Reserved
 *
 *        THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL
 *        The copyright notice above does not evidence any
 *        actual or intended publication of such source code.
 *
 *        This Module contains Proprietary Information of Marvell
 *        and should be treated as Confidential.
 *
 *        The information in this file is provided for the exclusive use of
 *        the licensees of Marvell. Such users have the right to use, modify,
 *        and incorporate this code into products for purposes authorized by
 *        the license agreement provided they include this notice and the
 *        associated copyright notice with any such product.
 *
 *        The information in this file is provided "AS IS" without warranty.
 *
 * Contents: PTP (Precision Time Protocol) support implementation
 *
 * Path  : OAK-Linux::OAK Switch Board - Linux Driver::Class Model::oak_ptp
 * Author: [Your Name]
 * Date  : [Current Date]
 */

#include "oak_ptp.h"
#include "oak_debug.h"

#ifdef CONFIG_PTP_1588_CLOCK

/* Name        : oak_ptp_read_clock
 * Returns     : u64
 * Parameters  : const struct cyclecounter *cc
 * Description : Read hardware clock counter
 */
u64 oak_ptp_read_clock(const struct cyclecounter *cc)
{
	oak_t *np = container_of(cc, oak_t, cc);
	u64 timestamp;
	
	/* TODO: Read hardware timestamp registers */
	/* Read 64-bit timestamp from hardware */
	/* timestamp = oak_unimac_io_read_32(np, OAK_PTP_CLOCK_TIME_NS); */
	/* timestamp |= ((u64)oak_unimac_io_read_32(np, OAK_PTP_CLOCK_TIME_SEC_LO)) << 32; */
	
	timestamp = 0; /* TODO: Implement hardware read */
	
	oakdbg(debug, PTP, "read_clock: timestamp=0x%llx", timestamp);
	return timestamp;
}

/* Name        : oak_ptp_adjfreq
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, s32 ppb
 * Description : Adjust PTP clock frequency
 */
int oak_ptp_adjfreq(struct ptp_clock_info *ptp, s32 ppb)
{
	oak_t *np = container_of(ptp, oak_t, ptp_caps);
	unsigned long flags;
	s64 adj;
	
	oakdbg(debug, PTP, "adjfreq: ppb=%d", ppb);
	
	if (ppb < -OAK_PTP_MAX_ADJ || ppb > OAK_PTP_MAX_ADJ)
		return -EINVAL;
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	
	/* TODO: Calculate frequency adjustment */
	/* adj = (s64)ppb * OAK_PTP_CLOCK_FREQ_HZ; */
	/* adj = div_s64(adj, 1000000000ULL); */
	
	/* TODO: Write frequency adjustment to hardware */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_FREQ_ADJ, (u32)adj); */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_CTRL, */
	/*                        oak_unimac_io_read_32(np, OAK_PTP_CLOCK_CTRL) | */
	/*                        OAK_PTP_CTRL_FREQ_ADJ); */
	
	spin_unlock_irqrestore(&np->ptp_lock, flags);
	
	return 0;
}

/* Name        : oak_ptp_adjtime
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, s64 delta
 * Description : Adjust PTP clock time by delta nanoseconds
 */
int oak_ptp_adjtime(struct ptp_clock_info *ptp, s64 delta)
{
	oak_t *np = container_of(ptp, oak_t, ptp_caps);
	unsigned long flags;
	
	oakdbg(debug, PTP, "adjtime: delta=%lld", delta);
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	
	/* TODO: Implement time adjustment */
	/* if (delta < 0) { */
	/*     delta = -delta; */
	/*     // Write negative offset */
	/* } else { */
	/*     // Write positive offset */
	/* } */
	
	/* TODO: Write time offset to hardware */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_OFFSET_NS, (u32)(delta & 0xFFFFFFFF)); */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_OFFSET_SEC_LO, (u32)(delta >> 32)); */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_CTRL, */
	/*                        oak_unimac_io_read_32(np, OAK_PTP_CLOCK_CTRL) | */
	/*                        OAK_PTP_CTRL_STEP_ADJ); */
	
	timecounter_adjtime(&np->tc, delta);
	
	spin_unlock_irqrestore(&np->ptp_lock, flags);
	
	return 0;
}

/* Name        : oak_ptp_gettime64
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, struct timespec64 *ts
 * Description : Get current PTP clock time
 */
int oak_ptp_gettime64(struct ptp_clock_info *ptp, struct timespec64 *ts)
{
	oak_t *np = container_of(ptp, oak_t, ptp_caps);
	unsigned long flags;
	u64 ns;
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	
	/* TODO: Update timecounter and get current time */
	ns = timecounter_read(&np->tc);
	
	spin_unlock_irqrestore(&np->ptp_lock, flags);
	
	*ts = ns_to_timespec64(ns);
	
	oakdbg(debug, PTP, "gettime64: sec=%lld nsec=%ld", ts->tv_sec, ts->tv_nsec);
	
	return 0;
}

/* Name        : oak_ptp_settime64
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, const struct timespec64 *ts
 * Description : Set PTP clock time
 */
int oak_ptp_settime64(struct ptp_clock_info *ptp, const struct timespec64 *ts)
{
	oak_t *np = container_of(ptp, oak_t, ptp_caps);
	unsigned long flags;
	u64 ns;
	
	oakdbg(debug, PTP, "settime64: sec=%lld nsec=%ld", ts->tv_sec, ts->tv_nsec);
	
	ns = timespec64_to_ns(ts);
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	
	/* TODO: Write time to hardware */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_TIME_NS, (u32)(ns & 0xFFFFFFFF)); */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_TIME_SEC_LO, (u32)(ts->tv_sec & 0xFFFFFFFF)); */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_TIME_SEC_HI, (u32)(ts->tv_sec >> 32)); */
	
	timecounter_init(&np->tc, &np->cc, ns);
	
	spin_unlock_irqrestore(&np->ptp_lock, flags);
	
	return 0;
}

/* Name        : oak_ptp_enable
 * Returns     : int
 * Parameters  : struct ptp_clock_info *ptp, struct ptp_clock_request *rq, int on
 * Description : Enable/disable PTP features
 */
int oak_ptp_enable(struct ptp_clock_info *ptp, struct ptp_clock_request *rq, int on)
{
	oak_t *np = container_of(ptp, oak_t, ptp_caps);
	
	oakdbg(debug, PTP, "enable: type=%d on=%d", rq->type, on);
	
	switch (rq->type) {
	case PTP_CLK_REQ_EXTTS:
		/* TODO: Implement external timestamp */
		return -EOPNOTSUPP;
		
	case PTP_CLK_REQ_PEROUT:
		/* TODO: Implement periodic output */
		return -EOPNOTSUPP;
		
	case PTP_CLK_REQ_PPS:
		/* TODO: Implement PPS output */
		return -EOPNOTSUPP;
		
	default:
		return -EOPNOTSUPP;
	}
}

/* Name        : oak_ptp_reset_clock
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Reset PTP hardware clock
 */
int oak_ptp_reset_clock(oak_t *np)
{
	unsigned long flags;
	u32 timeout = 1000;
	
	oakdbg(debug, PTP, "reset_clock");
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	
	/* TODO: Reset hardware clock */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_CTRL, OAK_PTP_CTRL_RESET); */
	
	/* TODO: Wait for reset completion */
	/* while (timeout-- > 0) { */
	/*     if (!(oak_unimac_io_read_32(np, OAK_PTP_CLOCK_CTRL) & OAK_PTP_CTRL_RESET)) */
	/*         break; */
	/*     udelay(10); */
	/* } */
	
	spin_unlock_irqrestore(&np->ptp_lock, flags);
	
	if (timeout == 0) {
		netdev_err(np->netdev, "PTP clock reset timeout\n");
		return -ETIMEDOUT;
	}
	
	return 0;
}

/* Name        : oak_ptp_update_timecounter
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Update software timecounter
 */
void oak_ptp_update_timecounter(oak_t *np)
{
	unsigned long flags;
	
	spin_lock_irqsave(&np->ptp_lock, flags);
	timecounter_read(&np->tc);
	spin_unlock_irqrestore(&np->ptp_lock, flags);
}

/* Name        : oak_ptp_init
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Initialize PTP clock and register with kernel
 */
int oak_ptp_init(oak_t *np)
{
	int err = 0;

	oakdbg(debug, PTP, "init");

	/* Initialize PTP lock */
	spin_lock_init(&np->ptp_lock);

	/* Initialize cyclecounter */
	np->cc.read = oak_ptp_read_clock;
	np->cc.mask = OAK_PTP_CYCLECOUNTER_MASK;
	np->cc.mult = 1;
	np->cc.shift = 0;

	/* Initialize timecounter */
	timecounter_init(&np->tc, &np->cc, ktime_to_ns(ktime_get_real()));

	/* Setup PTP clock capabilities */
	np->ptp_caps.owner = THIS_MODULE;
	snprintf(np->ptp_caps.name, sizeof(np->ptp_caps.name), "oak_ptp_%s",
		 pci_name(np->pdev));
	np->ptp_caps.max_adj = OAK_PTP_MAX_ADJ;
	np->ptp_caps.n_alarm = 0;
	np->ptp_caps.n_ext_ts = 0;
	np->ptp_caps.n_per_out = 0;
	np->ptp_caps.n_pins = 0;
	np->ptp_caps.pps = 0;
	np->ptp_caps.adjfreq = oak_ptp_adjfreq;
	np->ptp_caps.adjtime = oak_ptp_adjtime;
	np->ptp_caps.gettime64 = oak_ptp_gettime64;
	np->ptp_caps.settime64 = oak_ptp_settime64;
	np->ptp_caps.enable = oak_ptp_enable;

	/* TODO: Reset and initialize hardware clock */
	err = oak_ptp_reset_clock(np);
	if (err) {
		netdev_err(np->netdev, "Failed to reset PTP clock: %d\n", err);
		return err;
	}

	/* Register PTP clock */
	np->ptp_clock = ptp_clock_register(&np->ptp_caps, &np->pdev->dev);
	if (IS_ERR(np->ptp_clock)) {
		err = PTR_ERR(np->ptp_clock);
		np->ptp_clock = NULL;
		netdev_err(np->netdev, "Failed to register PTP clock: %d\n", err);
		return err;
	}

	np->ptp_enabled = 1;

	netdev_info(np->netdev, "PTP clock registered\n");

	return 0;
}

/* Name        : oak_ptp_remove
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Remove PTP clock and cleanup resources
 */
void oak_ptp_remove(oak_t *np)
{
	oakdbg(debug, PTP, "remove");

	if (np->ptp_clock) {
		ptp_clock_unregister(np->ptp_clock);
		np->ptp_clock = NULL;
		netdev_info(np->netdev, "PTP clock unregistered\n");
	}

	np->ptp_enabled = 0;
}

/* Name        : oak_ptp_start
 * Returns     : int
 * Parameters  : oak_t *np
 * Description : Start PTP clock operation
 */
int oak_ptp_start(oak_t *np)
{
	unsigned long flags;

	oakdbg(debug, PTP, "start");

	if (!np->ptp_enabled)
		return 0;

	spin_lock_irqsave(&np->ptp_lock, flags);

	/* TODO: Enable PTP hardware */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_CTRL, OAK_PTP_CTRL_ENABLE); */

	spin_unlock_irqrestore(&np->ptp_lock, flags);

	return 0;
}

/* Name        : oak_ptp_stop
 * Returns     : void
 * Parameters  : oak_t *np
 * Description : Stop PTP clock operation
 */
void oak_ptp_stop(oak_t *np)
{
	unsigned long flags;

	oakdbg(debug, PTP, "stop");

	if (!np->ptp_enabled)
		return;

	spin_lock_irqsave(&np->ptp_lock, flags);

	/* TODO: Disable PTP hardware */
	/* oak_unimac_io_write_32(np, OAK_PTP_CLOCK_CTRL, 0); */

	spin_unlock_irqrestore(&np->ptp_lock, flags);
}

/* Name        : oak_ptp_is_ptp_packet
 * Returns     : bool
 * Parameters  : struct sk_buff *skb
 * Description : Check if packet is a PTP packet
 */
bool oak_ptp_is_ptp_packet(struct sk_buff *skb)
{
	struct ethhdr *eth;
	struct iphdr *iph;
	struct udphdr *udph;
	__be16 proto;

	if (skb->len < ETH_HLEN)
		return false;

	eth = eth_hdr(skb);
	proto = eth->h_proto;

	/* Check for VLAN tag */
	if (proto == htons(ETH_P_8021Q)) {
		struct vlan_hdr *vhdr = (struct vlan_hdr *)(eth + 1);
		proto = vhdr->h_vlan_encapsulated_proto;
	}

	/* Check for PTP over Ethernet (IEEE 1588) */
	if (proto == htons(ETH_P_1588))
		return true;

	/* Check for PTP over UDP */
	if (proto == htons(ETH_P_IP)) {
		if (skb->len < ETH_HLEN + sizeof(struct iphdr))
			return false;

		iph = ip_hdr(skb);
		if (iph->protocol == IPPROTO_UDP) {
			if (skb->len < ETH_HLEN + sizeof(struct iphdr) + sizeof(struct udphdr))
				return false;

			udph = udp_hdr(skb);
			/* PTP event messages: port 319, general messages: port 320 */
			if (ntohs(udph->dest) == 319 || ntohs(udph->dest) == 320 ||
			    ntohs(udph->source) == 319 || ntohs(udph->source) == 320)
				return true;
		}
	}

	return false;
}

/* Name        : oak_ptp_tx_timestamp
 * Returns     : void
 * Parameters  : oak_t *np, struct sk_buff *skb, u32 timestamp
 * Description : Handle TX timestamp for PTP packets
 */
void oak_ptp_tx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp)
{
	struct skb_shared_hwtstamps shhwtstamps;
	u64 ns;
	unsigned long flags;

	if (!np->ptp_enabled || !oak_ptp_is_ptp_packet(skb))
		return;

	oakdbg(debug, PTP, "tx_timestamp: timestamp=0x%x", timestamp);

	spin_lock_irqsave(&np->ptp_lock, flags);

	/* TODO: Convert hardware timestamp to nanoseconds */
	/* ns = (u64)timestamp * NSEC_PER_SEC / OAK_PTP_CLOCK_FREQ_HZ; */
	ns = (u64)timestamp; /* TODO: Implement proper conversion */

	spin_unlock_irqrestore(&np->ptp_lock, flags);

	memset(&shhwtstamps, 0, sizeof(shhwtstamps));
	shhwtstamps.hwtstamp = ns_to_ktime(ns);

	skb_tstamp_tx(skb, &shhwtstamps);
}

/* Name        : oak_ptp_rx_timestamp
 * Returns     : void
 * Parameters  : oak_t *np, struct sk_buff *skb, u32 timestamp
 * Description : Handle RX timestamp for PTP packets
 */
void oak_ptp_rx_timestamp(oak_t *np, struct sk_buff *skb, u32 timestamp)
{
	struct skb_shared_hwtstamps *shhwtstamps;
	u64 ns;
	unsigned long flags;

	if (!np->ptp_enabled || !oak_ptp_is_ptp_packet(skb))
		return;

	oakdbg(debug, PTP, "rx_timestamp: timestamp=0x%x", timestamp);

	spin_lock_irqsave(&np->ptp_lock, flags);

	/* TODO: Convert hardware timestamp to nanoseconds */
	/* ns = (u64)timestamp * NSEC_PER_SEC / OAK_PTP_CLOCK_FREQ_HZ; */
	ns = (u64)timestamp; /* TODO: Implement proper conversion */

	spin_unlock_irqrestore(&np->ptp_lock, flags);

	shhwtstamps = skb_hwtstamps(skb);
	memset(shhwtstamps, 0, sizeof(*shhwtstamps));
	shhwtstamps->hwtstamp = ns_to_ktime(ns);
}

#endif /* CONFIG_PTP_1588_CLOCK */
